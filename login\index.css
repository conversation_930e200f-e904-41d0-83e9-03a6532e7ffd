:root {
  --default-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Ubuntu, "Helvetica Neue", Helvetica, Arial, "PingFang SC",
    "Hiragino Sans GB", "Microsoft Yahei UI", "Microsoft Yahei",
    "Source Han Sans CN", sans-serif;
}

.main-container {
  overflow: hidden;
}

.main-container,
.main-container * {
  box-sizing: border-box;
}

input,
select,
textarea,
button {
  outline: 0;
}

.main-container {
  position: relative;
  width: 440px;
  height: 956px;
  margin: 0 auto;
  background: #ffffff;
  border: 1px solid #000000;
  overflow: hidden;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}
.bar-status {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  flex-wrap: nowrap;
  position: relative;
  width: 440px;
  height: 56px;
  margin: 0 0 0 0;
  padding: 15px 46px 15px 46px;
  background: #ffffff;
  overflow: hidden;
}
.clock {
  flex-shrink: 0;
  position: relative;
  width: 54px;
  height: 20px;
  z-index: 1;
}
.clock-1 {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: absolute;
  width: 54px;
  height: 20px;
  top: 0;
  left: 0;
  color: #000000;
  font-family: Poppins, var(--default-font-family);
  font-size: 17px;
  font-weight: 600;
  line-height: 20px;
  text-align: center;
  white-space: nowrap;
  letter-spacing: -0.41px;
  z-index: 2;
}
.right-side {
  flex-shrink: 0;
  position: relative;
  width: 77.401px;
  height: 13px;
  z-index: 3;
}
.status-bar-battery {
  position: absolute;
  width: 27.401px;
  height: 13px;
  top: 0;
  left: 50%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/k4DS8ffypQ.png)
    no-repeat center;
  background-size: cover;
  transform: translate(41.24%, 0);
  z-index: 4;
}
.wifi {
  position: absolute;
  width: 21.96%;
  height: 91.03%;
  top: 7.69%;
  left: 33.59%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/DPfNM3Aw0J.png)
    no-repeat center;
  background-size: 100% 100%;
  z-index: 5;
}
.icon-mobile-signal {
  position: absolute;
  width: 18px;
  height: 12px;
  top: 1px;
  left: 50%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/F7K3kAW7u7.png)
    no-repeat center;
  background-size: cover;
  transform: translate(-215%, 0);
  z-index: 6;
}
.main-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: nowrap;
  gap: 16px;
  position: relative;
  width: 362px;
  margin: 194.5px 0 0 39px;
  padding: 10px 10px 10px 10px;
  z-index: 7;
}
.header {
  align-self: stretch;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  min-width: 0;
  height: 48px;
  color: #67c16d;
  font-family: Poppins, var(--default-font-family);
  font-size: 32px;
  font-weight: 700;
  line-height: 48px;
  text-align: center;
  white-space: nowrap;
  z-index: 8;
}
.bar {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  flex-shrink: 0;
  position: relative;
  width: 342px;
  padding: 4px 4px 4px 4px;
  background: #f3faf3;
  z-index: 9;
  border-radius: 8px;
}
.sign-up-container {
  flex-shrink: 0;
  position: relative;
  width: 167px;
  height: 35px;
  background: #c7ebc9;
  z-index: 10;
  border-radius: 8px;
}
.sign-up-text {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  height: 27px;
  top: 4px;
  left: 54.5px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  line-height: 27px;
  text-align: left;
  white-space: nowrap;
  z-index: 11;
}
.register-container {
  flex-shrink: 0;
  position: relative;
  width: 167px;
  height: 35px;
  background: #f3faf3;
  z-index: 12;
  border-radius: 8px;
}
.register-text {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  height: 27px;
  top: 4px;
  left: 39.5px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  line-height: 27px;
  text-align: left;
  white-space: nowrap;
  z-index: 13;
}
.form-login {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 16px;
  position: relative;
  min-width: 0;
  z-index: 14;
}
.email-textbar {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 4px;
  position: relative;
  z-index: 15;
}
.email-label {
  align-self: stretch;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 27px;
  color: #000000;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  line-height: 27px;
  text-align: left;
  white-space: nowrap;
  z-index: 16;
}
.email-input-container {
  display: flex;
  align-items: center;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 10px;
  position: relative;
  padding: 10px 10px 10px 10px;
  border-top: 1px solid #e0e0e0;
  z-index: 17;
}
.email-placeholder {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 21px;
  color: #979797;
  font-family: Poppins, var(--default-font-family);
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
  white-space: nowrap;
  z-index: 18;
}
.password-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 18px;
  position: relative;
  z-index: 19;
}
.password-textbar {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 4px;
  position: relative;
  z-index: 20;
}
.password-label {
  align-self: stretch;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 27px;
  color: #000000;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  line-height: 27px;
  text-align: left;
  white-space: nowrap;
  z-index: 21;
}
.password-input-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  position: relative;
  padding: 10px 10px 10px 10px;
  border-top: 1px solid #e0e0e0;
  z-index: 22;
}
.password-placeholder {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 21px;
  color: #979797;
  font-family: Poppins, var(--default-font-family);
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
  white-space: nowrap;
  z-index: 23;
}
.password-visibility-icon {
  flex-shrink: 0;
  position: relative;
  width: 16px;
  height: 16px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/533Dw5UmgC.png)
    no-repeat center;
  background-size: cover;
  z-index: 24;
}
.button {
  flex-shrink: 0;
  position: relative;
  width: 343px;
  height: 35px;
  font-size: 0px;
  z-index: 25;
  border-radius: 8px;
}
.login-2 {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 73px;
  height: 30.484px;
  margin: 2.5px 0 0 134.5px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  line-height: 27px;
  text-align: center;
  white-space: nowrap;
  z-index: 27;
}
.rectangle {
  position: absolute;
  width: 343px;
  height: 35px;
  top: 0;
  left: 0;
  background: #c7ebc9;
  z-index: 26;
  border-radius: 8px;
}
.frame {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 21px;
  position: relative;
  width: 191.353px;
  z-index: 28;
}
.other-sign-in-options {
  align-self: stretch;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  min-width: 0;
  height: 27px;
  color: #000000;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 400;
  line-height: 27px;
  text-align: left;
  white-space: nowrap;
  z-index: 29;
}
.frame-3 {
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 32px;
  position: relative;
  min-width: 0;
  z-index: 30;
}
.devicon-google {
  flex-shrink: 0;
  position: relative;
  width: 40px;
  height: 40px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/MxNhonxGGn.png)
    no-repeat center;
  background-size: cover;
  z-index: 31;
}
.devicon-facebook {
  flex-shrink: 0;
  position: relative;
  width: 40px;
  height: 40px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/aqEXB4wYLw.png)
    no-repeat center;
  background-size: cover;
  z-index: 32;
}
.group {
  flex-shrink: 0;
  position: relative;
  width: 40px;
  height: 40px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/yYGp93ehK1.png)
    no-repeat center;
  background-size: cover;
  z-index: 33;
}
